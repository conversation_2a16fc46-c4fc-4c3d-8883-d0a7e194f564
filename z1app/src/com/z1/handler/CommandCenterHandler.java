package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.Utils.ResponseMessage.Status;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.DeserializationConfig.Feature;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import z1.analytics.datalake.querybuilders.audience.AudienceBuilder;
import z1.c3.CustomConfig;
import z1.commons.Const;
import z1.commons.def.RuleDef;
import z1.datalake.CloudInfraSetup;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static z1.analytics.datalake.querybuilders.audience.AudienceBuilder.AUDIENCE_QRY_SUBTYPE;

public class CommandCenterHandler implements CommandHandlerFactory
{
  private static final String REFERRERS_SCHEMA;
  private static final JsonMarshaller jm = Const.jsonMarshaller;
  private static final ObjectMapper mapper = new ObjectMapper();

  static
  {
    REFERRERS_SCHEMA = Utils.loadFileResource("META-INF/referrers.json");
    mapper.configure(Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
  }

  private enum PostCommand
  {
    queryAudienceBuilder,
  }

  private enum GetCommand
  {
    referrers,
    categories,
    computeAnalyticsOnDemand
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
                         final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        switch (command)
        {
          // c3/data/commandcenter/referrers
          case referrers:
          {
            Map<String, Object> payload = jm.readAsMap(REFERRERS_SCHEMA);
            resp.getWriter().print(jm.serialize(payload));
            break;
          }
          // c3/data/commandcenter/categories
          case categories:
          {
            // return empty list if there are no categories saved
            String payload = "[]";
            CustomConfig cc = CustomConfig.load(uctx, CustomConfig.Type.audienceCategories, true);
            if (cc != null && cc.getPayload() != null && !cc.getPayload().isEmpty())
            {
              payload = cc.getPayload();
            }

            // sort categories
            List<Map<String, Object>> categories = jm.readAsList(payload);
            List<Map<String, Object>> sortedList = categories.stream()
              .sorted(Comparator.comparing(map -> (String) map.get("displayName")))
              .collect(Collectors.toList());

            resp.getWriter().print(jm.serialize(sortedList));
            break;
          }
          // c3/data/commandcenter/computeAnalyticsOnDemand?type=<data_by_model|data_by_sessions>
          case computeAnalyticsOnDemand:
          {
            ULogger logger = uctx.getLogger(getClass());
            String type = req.getParameter("type");

            String response = String.format(
                "On-demand metrics job for type %s submitted successfully. It will take a few minutes to complete.",
                type);

            if (CloudInfraSetup.CloudJob.getJob(type) == null)
            {
              String jobs = Arrays.stream(CloudInfraSetup.CloudJob.values())
                  .map(CloudInfraSetup.CloudJob::name)
                  .collect(Collectors.joining(", "));
              resp.getWriter().print("Invalid type. Valid types are " + jobs);
              break;
            }

            String scriptPath = z1.commons.Const.SCRIPT_ROOT
                + z1.commons.Const.SEP + "datalake" + z1.commons.Const.SEP
                + CloudInfraSetup.Type.AWS.getValue() + Const.SEP + "jobcrud";
            String cmd = String.format(
                "python run-glue-job.py --namespace %s --jobname %s",
                uctx.getNamespace(),
                z1.commons.Utils.addAccountNumInJobName(type, uctx));

            String[] cmdParts = cmd.split(" ");
            File workingDir = new File(scriptPath);

            ExecutorService executor = Executors.newSingleThreadExecutor();
            String finalCmd = cmd;
            executor.submit(() -> {
              try
              {
                logger.log("Async job starting with cmd: " + finalCmd);
                ProcessBuilder pb = new ProcessBuilder(cmdParts);
                pb.directory(workingDir);
                pb.redirectErrorStream(true);

                Map<String, String> env = pb.environment();
                String scriptRootPath = new File(z1.commons.Const.SCRIPT_ROOT)
                    .getParent();
                env.put("PYTHONPATH", scriptRootPath);

                Process process = pb.start();

                try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(),
                        StandardCharsets.UTF_8)))
                {
                  String line;
                  while ((line = reader.readLine()) != null)
                  {
                    logger.log("ETL Job Output: " + line);
                  }
                }
              }
              catch (IOException e)
              {
                logger.error("Failed to start async job: " + e.getMessage());
              }
            });
            executor.shutdown();

            resp.getWriter().print(response);
            break;
          }
          default:
          {
            break;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
                         final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        switch (command)
        {
          // c3/data/commandcenter/queryAudienceBuilder?type=[all|chart|metrics]
          case queryAudienceBuilder:
          {
            try
            {
              String payload = ServletUtil.getPayload(req);

              String qtype = req.getParameter("type");
              String udcTkey = req.getHeader("x-udc-tkey");

              Map<String, Object> params = new HashMap<>();
              params.put("udcTkey", udcTkey);
              params.put(AUDIENCE_QRY_SUBTYPE, qtype);

              // Parse payload as List<RuleDef>
              ObjectMapper mapper = new ObjectMapper();
              mapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
              List<RuleDef> rules = mapper.readValue(payload, new TypeReference<List<RuleDef>>() {});

              // Build reponse for UI
              Map<String, Object> result = AudienceBuilder.executeQuery(uctx, rules, params);
              resp.getWriter().print(Const.jsonMarshaller.serialize(result));
              return;
            }
            catch (Exception e)
            {
              z1.commons.Utils.showStackTraceIfEnable(e, null);

              ULogger logger = uctx.getLogger(getClass());
              if (logger.canLog())
              {
                logger.log(e.getMessage());
              }

              resp.getWriter().print(new ResponseMessage(uctx, Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Exception when processing audience query. Reason: "+e.getMessage()));
            }
            break;
          }
          default:
          {
            break;
          }
        }
      }
    };
  }
}
