package com.z1.Utils;

import java.util.List;
import java.util.Map;

import org.apache.hadoop.hbase.util.Bytes;
import udichi.core.util.JsonMarshaller;
import z1.commons.Const;
import z1.core.Activity;
import z1.core.utils.ActivityIterator;
import z1.core.utils.TimeUtils;

/**
 * Serializes activities to send to UI.
 */
public class ActivitySerializer
{

  /**
   * Serializes an activity iterator to create a JSON list string. It creates 2
   * attributes - date and activity.
   * 
   * @param it
   * @param max
   * @param max
   * @return A JSON serialized list.
   * 
   */
  public static String serialize(ActivityIterator it, int max)
  {
    if (it.isNull()) return null;

    JsonMarshaller jm = new JsonMarshaller();
    List<Map<String, Object>> list = new java.util.ArrayList<>(10);
    int limit = 0;

    while (it.hasNext() && (limit < max))
    {
      Map<String, Object> map = new java.util.HashMap<>(10);

      Activity a = it.next();
      String time = a.getTime();
      String act = a.getName();
      String chnl = a.getValue(Const.P_EVENT_SOURCE);
      String event = a.getValue(Const.P_EVENT_NAME);
      String sessionId = a.getValue(Const.P_SESSION_ID);

      if ((time == null) || (act == null)) continue;

      TimeUtils tu = new TimeUtils();
      Long millis = tu.parseTime(time);

      map.put("time", millis);
      map.put("timestamp", time);
      map.put("activity", act);
      map.put("channel", chnl);
      map.put("eventName", event);
      map.put("sessionId", sessionId);

      limit++;
      list.add(map);
    }

    return jm.serialize(list);
  }

  /**
   * Defaults the fetch to max int value
   * 
   * @param it
   * @return
   */
  public static String serialize(ActivityIterator it)
  {
    return serialize(it, Integer.MAX_VALUE);
  }

  /**
   * Utility method for serializing hbase row key
   * 
   * @param rowKey
   * @return
   */
  public static String serializeRowKey(byte[] rowKey)
  {
    return Bytes.toString(rowKey);
  }

}
