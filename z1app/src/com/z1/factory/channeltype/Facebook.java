package com.z1.factory.channeltype;

import java.util.List;
import java.util.Map;

import z1.channel.def.CredentialsDef;

import com.z1.registration.def.UrlInfoType;
import com.z1social.fb.PageSearch;

public class Facebook implements IChannelType
{

  private enum channelInfo
  {
    channelType,
    companyName,
    competitors,
    industry,
    url
  }
  
  @Override
  public String getAccessToken()
  {
    // Access token comes from UI if not use this.
    return "409851409149213|rhzi9I_-qscAgA3gGw7YeGlnhaE";
  }

  @Override
  public String getFeedHandler()
  {
    return "com.z1social.channel.FBPageFeedHandler";
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByCompanyName(
      Map<String, Object> channelInput)
  {
    PageSearch ps = new PageSearch();

    return ps.fetchPages(getAccessToken(),
        (String) channelInput.get(channelInfo.companyName.name()),
        (String) channelInput.get(channelInfo.industry.name()));
  }

  @Override
  public List<Map<String, Object>> getChannelInfoByUrl(
      Map<String, Object> channelInput)
  {
    PageSearch ps = new PageSearch();

    return ps.fetchPageByURL(getAccessToken(),
        (String) channelInput.get(channelInfo.url.name()));
    
  }

  @Override
  public void setCredentialDef(UrlInfoType channelUrl, CredentialsDef cd)
  {
    if (channelUrl.getAccessToken() != null
        && channelUrl.getAccessToken().trim().length() > 0)
    {
      cd.setAccessToken(channelUrl.getAccessToken());
    }
    else
    {
      cd.setAccessToken(getAccessToken());// If accesstoken not send
                                          // from ui use the one arnab send.
    }
    
  }

}
